# 🔧 Phân Tích Cải Thiện Tính Năng Streak

## 🚨 Các Vấn Đề Hardcode Cần Khắc Phục

### 1. **Magic Numbers - Số Câu Hỏi**

#### ❌ **Vấn đề hiện tại:**
```javascript
// Hardcode số câu hỏi và index
if (i === 2) {
    setIsQuestion2(true);
} else if (i === 3) {
    // Hoàn thành
}

setIndexQuestionActive(2);  // Hardcode câu thứ 3
setQuestionActive(questions[2]);  // Hardcode index

if (indexQuestionActive === 2) {
    // Logic cho câu cuối
}
```

#### ✅ **Giải pháp:**
```javascript
// Tạo constants
const STREAK_CONFIG = {
    TOTAL_QUESTIONS: 3,
    MANDATORY_QUESTIONS: 2,
    OPTIONAL_QUESTION_INDEX: 2,
    CHOICE_SCREEN_TRIGGER: 2
};

// Sử dụng constants
if (i === STREAK_CONFIG.CHOICE_SCREEN_TRIGGER) {
    setIsQuestion2(true);
} else if (i === STREAK_CONFIG.TOTAL_QUESTIONS) {
    // Hoàn thành
}

const isLastQuestion = indexQuestionActive === STREAK_CONFIG.TOTAL_QUESTIONS - 1;
const isOptionalQuestion = indexQuestionActive === STREAK_CONFIG.OPTIONAL_QUESTION_INDEX;
```

### 2. **Timer Intervals Hardcode**

#### ❌ **Vấn đề hiện tại:**
```javascript
// Hardcode timer interval và auto-save frequency
setInterval(() => {
    setTime(time + 1);
    if (time % 10 === 0) {  // Hardcode 10 giây
        strapi.streak.updateTime({documentId: dayActive.documentId, time:time});
    }
}, 1000);  // Hardcode 1 giây
```

#### ✅ **Giải pháp:**
```javascript
const TIMER_CONFIG = {
    INTERVAL_MS: 1000,           // 1 giây
    AUTO_SAVE_FREQUENCY: 10,     // Mỗi 10 giây
    MAX_TIME_LIMIT: 1800         // 30 phút tối đa
};

setInterval(() => {
    setTime(prevTime => {
        const newTime = prevTime + 1;
        
        // Auto-save theo config
        if (newTime % TIMER_CONFIG.AUTO_SAVE_FREQUENCY === 0) {
            strapi.streak.updateTime({
                documentId: dayActive.documentId, 
                time: newTime
            });
        }
        
        // Timeout protection
        if (newTime >= TIMER_CONFIG.MAX_TIME_LIMIT) {
            handleTimeOut();
        }
        
        return newTime;
    });
}, TIMER_CONFIG.INTERVAL_MS);
```

### 3. **Điểm Số Hardcode**

#### ❌ **Vấn đề hiện tại:**
```javascript
// Hardcode star point multiplier
const finalPoint = userAnswer.is_star_point ? 
    questionActive.exercise_type.point * 2 :  // Hardcode x2
    questionActive.exercise_type.point;

// Backend hardcode
sum(case when is_star_point = 1 then nvl(point_,0) *2 else nvl(point_,0) end)
```

#### ✅ **Giải pháp:**
```javascript
const SCORING_CONFIG = {
    STAR_POINT_MULTIPLIER: 2,
    MAX_STAR_POINT_USAGE: 1,
    BONUS_COMPLETION_POINTS: 5,
    PERFECT_STREAK_BONUS: 10
};

// Frontend
const calculateQuestionPoint = (question, isStarPoint) => {
    const basePoint = question.exercise_type.point;
    return isStarPoint ? 
        basePoint * SCORING_CONFIG.STAR_POINT_MULTIPLIER : 
        basePoint;
};

// Backend - tạo function
CREATE FUNCTION calculate_streak_points(
    user_id INT, 
    streak_question_id INT,
    star_multiplier DECIMAL DEFAULT 2.0
) RETURNS INT
```

### 4. **Error Messages Hardcode**

#### ❌ **Vấn đề hiện tại:**
```javascript
// Hardcode error messages
showNotification({
    type: "error",
    title: "Streak không tìm thấy",
    message: "Nhắn cho Ba để thêm thông tin streak nhé bây!"
});

toast.error('Điền đáp án đã nhé!');
```

#### ✅ **Giải pháp:**
```javascript
const MESSAGES = {
    ERRORS: {
        STREAK_NOT_FOUND: {
            title: "Streak không tìm thấy",
            message: "Vui lòng liên hệ admin để thêm streak cho ngày hôm nay"
        },
        NO_ANSWER_SELECTED: "Vui lòng chọn đáp án trước khi tiếp tục",
        NETWORK_ERROR: "Lỗi kết nối, vui lòng thử lại",
        TIME_OUT: "Hết thời gian làm bài"
    },
    SUCCESS: {
        STREAK_COMPLETED: "Chúc mừng! Bạn đã hoàn thành streak hôm nay",
        ANSWER_SAVED: "Đã lưu câu trả lời"
    }
};

// Sử dụng
showNotification({
    type: "error",
    ...MESSAGES.ERRORS.STREAK_NOT_FOUND
});
```

### 5. **Course ID Hardcode**

#### ❌ **Vấn đề hiện tại:**
```javascript
// Hardcode lấy course đầu tiên
const orders = await strapi.orders.getOrderByUser(user_.id,'course');
const res = await strapi.streak.getStreak(formatted, orders.data[0].course.id);

// Backend hardcode user_id
const user_id = 4;  // Hardcode trong rawTotal function
```

#### ✅ **Giải pháp:**
```javascript
// Frontend - xử lý multiple courses
const getUserActiveCourse = async (userId) => {
    const orders = await strapi.orders.getOrderByUser(userId, 'course');
    
    if (!orders?.data?.length) {
        throw new Error(MESSAGES.ERRORS.NO_ACTIVE_COURSE);
    }
    
    // Lấy course active hoặc course mới nhất
    const activeCourse = orders.data.find(order => order.status === 'active') 
        || orders.data[0];
    
    return activeCourse.course.id;
};

// Backend - remove hardcode
async rawTotal(ctx) {
    const { user_id } = ctx.request.body;
    
    if (!user_id) {
        return ctx.badRequest('user_id is required');
    }
    
    const rawQuery = `
        SELECT COUNT(*) as total
        FROM streaks
        WHERE user_id = ?
    `;
    const result = await strapi.db.connection.raw(rawQuery, [user_id]);
    // ...
}
```

### 6. **UI Text Hardcode**

#### ❌ **Vấn đề hiện tại:**
```javascript
// Hardcode UI text
<p>Tiếp theo là <strong>câu hỏi khó</strong> á nha. Tự tin thử sức đi!</p>
<button>Hoàn thành streak</button>
<button>Thử sức</button>

// Star point modal
<p>Mại dzô, đặt sao hy vọng đi nè!</p>
<p>Sao hy vọng giúp bạn x2 số điểm...</p>
```

#### ✅ **Giải pháp:**
```javascript
const UI_TEXTS = {
    CHOICE_SCREEN: {
        TITLE: "Tiếp theo là câu hỏi khó",
        DESCRIPTION: "Tự tin thử sức đi! Nếu thoát bây giờ, điểm của 2 câu trước sẽ không được tính.",
        COMPLETE_BUTTON: "Hoàn thành streak",
        CONTINUE_BUTTON: "Thử sức"
    },
    STAR_POINT: {
        MODAL_TITLE: "Sử dụng sao hy vọng",
        MODAL_DESCRIPTION: "Sao hy vọng sẽ nhân đôi điểm số câu này. Bạn chỉ được dùng 1 lần trong streak hôm nay.",
        BUTTON_USE: "Đặt sao hy vọng",
        BUTTON_USED: "Đã đặt sao hy vọng"
    }
};

// Component
<p>{UI_TEXTS.CHOICE_SCREEN.TITLE}</p>
<p>{UI_TEXTS.CHOICE_SCREEN.DESCRIPTION}</p>
```

## 🏗️ Cải Thiện Kiến Trúc

### 1. **Tạo Configuration System**

```javascript
// config/streak.config.js
export const STREAK_CONFIG = {
    // Câu hỏi
    QUESTIONS: {
        TOTAL: 3,
        MANDATORY: 2,
        OPTIONAL_INDEX: 2,
        CHOICE_TRIGGER: 2
    },
    
    // Timer
    TIMER: {
        INTERVAL_MS: 1000,
        AUTO_SAVE_FREQUENCY: 10,
        MAX_TIME_LIMIT: 1800,
        WARNING_TIME: 1500
    },
    
    // Điểm số
    SCORING: {
        STAR_POINT_MULTIPLIER: 2,
        MAX_STAR_USAGE: 1,
        COMPLETION_BONUS: 5,
        PERFECT_BONUS: 10
    },
    
    // Validation
    VALIDATION: {
        MIN_ANSWER_LENGTH: 1,
        MAX_RETRY_ATTEMPTS: 3,
        NETWORK_TIMEOUT: 5000
    }
};
```

### 2. **Tạo Utility Functions**

```javascript
// utils/streakUtils.js
export const StreakUtils = {
    // Kiểm tra trạng thái câu hỏi
    isChoiceScreen: (questionIndex) => 
        questionIndex === STREAK_CONFIG.QUESTIONS.CHOICE_TRIGGER,
    
    isLastQuestion: (questionIndex) => 
        questionIndex === STREAK_CONFIG.QUESTIONS.TOTAL - 1,
    
    isOptionalQuestion: (questionIndex) => 
        questionIndex === STREAK_CONFIG.QUESTIONS.OPTIONAL_INDEX,
    
    // Tính điểm
    calculatePoints: (questions, answers) => {
        return questions.reduce((total, question, index) => {
            const answer = answers[index];
            if (!answer?.is_correct) return total;
            
            const basePoint = question.exercise_type.point;
            const multiplier = answer.is_star_point ? 
                STREAK_CONFIG.SCORING.STAR_POINT_MULTIPLIER : 1;
            
            return total + (basePoint * multiplier);
        }, 0);
    },
    
    // Format thời gian
    formatTime: (seconds) => {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
};
```

### 3. **Tạo Custom Hooks**

```javascript
// hooks/useStreakTimer.js
export const useStreakTimer = (isActive, onAutoSave) => {
    const [time, setTime] = useState(0);
    const [timer, setTimer] = useState(null);
    
    useEffect(() => {
        if (!isActive) {
            clearInterval(timer);
            return;
        }
        
        const interval = setInterval(() => {
            setTime(prevTime => {
                const newTime = prevTime + 1;
                
                // Auto-save
                if (newTime % STREAK_CONFIG.TIMER.AUTO_SAVE_FREQUENCY === 0) {
                    onAutoSave(newTime);
                }
                
                // Timeout warning
                if (newTime === STREAK_CONFIG.TIMER.WARNING_TIME) {
                    showTimeWarning();
                }
                
                return newTime;
            });
        }, STREAK_CONFIG.TIMER.INTERVAL_MS);
        
        setTimer(interval);
        
        return () => clearInterval(interval);
    }, [isActive, onAutoSave]);
    
    return { time, setTime, resetTimer: () => setTime(0) };
};
```

## 🔄 Cải Thiện State Management

### 1. **Tạo Streak Context**

```javascript
// context/StreakContext.js
const StreakContext = createContext();

export const StreakProvider = ({ children }) => {
    const [streakState, setStreakState] = useReducer(streakReducer, {
        // Question state
        questions: [],
        currentQuestionIndex: 0,
        currentQuestion: null,

        // UI state
        isStarted: false,
        isFinished: false,
        isChoiceScreen: false,
        isViewingAnswer: false,

        // User data
        userAnswer: { answer: "", is_correct: false, is_star_point: false },
        answers: [],

        // Timer
        time: 0,

        // Star point
        hasUsedStarPoint: false,

        // Progress
        progress: 0
    });

    const actions = {
        startStreak: () => dispatch({ type: 'START_STREAK' }),
        nextQuestion: () => dispatch({ type: 'NEXT_QUESTION' }),
        showChoiceScreen: () => dispatch({ type: 'SHOW_CHOICE_SCREEN' }),
        finishStreak: () => dispatch({ type: 'FINISH_STREAK' }),
        saveAnswer: (answer) => dispatch({ type: 'SAVE_ANSWER', payload: answer }),
        useStarPoint: () => dispatch({ type: 'USE_STAR_POINT' }),
        updateTime: (time) => dispatch({ type: 'UPDATE_TIME', payload: time })
    };

    return (
        <StreakContext.Provider value={{ state: streakState, actions }}>
            {children}
        </StreakContext.Provider>
    );
};
```

### 2. **Tạo API Service Layer**

```javascript
// services/streakService.js
export class StreakService {
    static async getTodayStreak(userId, courseId) {
        try {
            const date = new Date().toISOString().split('T')[0];
            const response = await strapi.streak.getStreak(date, courseId);

            if (!response?.data?.data?.length) {
                throw new StreakError(
                    MESSAGES.ERRORS.STREAK_NOT_FOUND.title,
                    MESSAGES.ERRORS.STREAK_NOT_FOUND.message
                );
            }

            return response.data.data[0];
        } catch (error) {
            throw new StreakError('Network Error', error.message);
        }
    }

    static async createStreakSession(userId, streakQuestionId) {
        const data = {
            isJoin: false,
            user_id: userId,
            streak_question_id: streakQuestionId
        };

        const response = await strapi.streak.createStreak(data);

        if (response?.status !== 201) {
            throw new StreakError('Failed to create streak session');
        }

        return response.data.data;
    }

    static async saveAnswer(answerData) {
        // Validation
        if (!answerData.answer) {
            throw new ValidationError(MESSAGES.ERRORS.NO_ANSWER_SELECTED);
        }

        const response = await strapi.streak.saveQuestionAnswer(answerData);

        if (response?.status !== 201) {
            throw new StreakError('Failed to save answer');
        }

        return response.data;
    }

    static async resumeStreak(userId, streakId) {
        const answers = await strapi.streak.getAnswerStreakByUser({
            user: userId,
            streakId: streakId
        });

        return {
            answers: answers.data || [],
            resumeIndex: answers.data?.length || 0,
            hasUsedStarPoint: answers.data?.some(a => a.is_star_point) || false
        };
    }
}

// Custom error classes
class StreakError extends Error {
    constructor(title, message) {
        super(message);
        this.title = title;
        this.name = 'StreakError';
    }
}

class ValidationError extends Error {
    constructor(message) {
        super(message);
        this.name = 'ValidationError';
    }
}
```

## 🎯 Cải Thiện Business Logic

### 1. **Tạo Streak Flow Manager**

```javascript
// managers/StreakFlowManager.js
export class StreakFlowManager {
    constructor(config = STREAK_CONFIG) {
        this.config = config;
    }

    // Xác định bước tiếp theo
    getNextStep(currentIndex, totalAnswered) {
        if (currentIndex < this.config.QUESTIONS.MANDATORY - 1) {
            return { type: 'NEXT_QUESTION', index: currentIndex + 1 };
        }

        if (currentIndex === this.config.QUESTIONS.MANDATORY - 1) {
            return { type: 'SHOW_CHOICE', index: currentIndex + 1 };
        }

        if (currentIndex === this.config.QUESTIONS.TOTAL - 1) {
            return { type: 'FINISH_STREAK' };
        }

        return { type: 'NEXT_QUESTION', index: currentIndex + 1 };
    }

    // Kiểm tra có thể sử dụng star point
    canUseStarPoint(hasUsedBefore, isAnswerSubmitted) {
        return !hasUsedBefore && !isAnswerSubmitted;
    }

    // Tính toán progress
    calculateProgress(currentIndex, isChoiceScreen = false) {
        if (isChoiceScreen) {
            return (this.config.QUESTIONS.MANDATORY / this.config.QUESTIONS.TOTAL) * 100;
        }

        return ((currentIndex + 1) / this.config.QUESTIONS.TOTAL) * 100;
    }

    // Validate streak completion
    validateCompletion(answers) {
        const mandatoryAnswers = answers.slice(0, this.config.QUESTIONS.MANDATORY);
        return mandatoryAnswers.length === this.config.QUESTIONS.MANDATORY;
    }
}
```

### 2. **Tạo Validation System**

```javascript
// validators/streakValidators.js
export const StreakValidators = {
    validateAnswer: (answer) => {
        const errors = [];

        if (!answer || !answer.answer) {
            errors.push(MESSAGES.ERRORS.NO_ANSWER_SELECTED);
        }

        if (answer.answer && answer.answer.length < STREAK_CONFIG.VALIDATION.MIN_ANSWER_LENGTH) {
            errors.push('Đáp án không hợp lệ');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    },

    validateStreakData: (streakData) => {
        const errors = [];

        if (!streakData.questions || streakData.questions.length !== STREAK_CONFIG.QUESTIONS.TOTAL) {
            errors.push('Dữ liệu câu hỏi không đầy đủ');
        }

        if (!streakData.streakId) {
            errors.push('Không tìm thấy ID streak');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    },

    validateTimeLimit: (currentTime) => {
        return currentTime < STREAK_CONFIG.TIMER.MAX_TIME_LIMIT;
    }
};
```

## 📊 Database Improvements

### 1. **Tạo Stored Procedures**

```sql
-- Stored procedure tính điểm streak
DELIMITER //
CREATE PROCEDURE CalculateStreakScore(
    IN p_user_id INT,
    IN p_streak_question_id INT,
    IN p_star_multiplier DECIMAL(3,2) DEFAULT 2.0,
    OUT p_total_score INT,
    OUT p_correct_answers INT,
    OUT p_total_questions INT
)
BEGIN
    SELECT
        SUM(CASE
            WHEN qa.is_correct = 1 THEN
                CASE WHEN qa.is_star_point = 1
                    THEN et.point * p_star_multiplier
                    ELSE et.point
                END
            ELSE 0
        END) as total_score,
        SUM(CASE WHEN qa.is_correct = 1 THEN 1 ELSE 0 END) as correct_answers,
        COUNT(*) as total_questions
    INTO p_total_score, p_correct_answers, p_total_questions
    FROM questions_answers qa
    JOIN questions q ON qa.question_id = q.id
    JOIN exercise_types et ON q.exercise_type_id = et.id
    WHERE qa.user_id = p_user_id
    AND qa.streak_question_id = p_streak_question_id;
END //
DELIMITER ;
```

### 2. **Tạo Configuration Table**

```sql
-- Bảng cấu hình streak
CREATE TABLE streak_configurations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value JSON NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default configs
INSERT INTO streak_configurations (config_key, config_value, description) VALUES
('questions_config', '{"total": 3, "mandatory": 2, "optional_index": 2}', 'Cấu hình số lượng câu hỏi'),
('timer_config', '{"interval_ms": 1000, "auto_save_frequency": 10, "max_time_limit": 1800}', 'Cấu hình timer'),
('scoring_config', '{"star_point_multiplier": 2, "max_star_usage": 1, "completion_bonus": 5}', 'Cấu hình điểm số'),
('ui_texts', '{"choice_screen": {"title": "Tiếp theo là câu hỏi khó", "description": "Tự tin thử sức đi!"}}', 'Cấu hình text UI');
```

## 🚀 Implementation Priority

### **Phase 1: Critical Fixes (Week 1)**
1. ✅ Tạo constants cho magic numbers
2. ✅ Fix hardcode user_id trong backend
3. ✅ Tạo error message constants
4. ✅ Validation cho API inputs

### **Phase 2: Architecture Improvements (Week 2)**
1. ✅ Tạo configuration system
2. ✅ Implement utility functions
3. ✅ Create custom hooks
4. ✅ Add proper error handling

### **Phase 3: Advanced Features (Week 3)**
1. ✅ Implement Context API
2. ✅ Create service layer
3. ✅ Add comprehensive validation
4. ✅ Database optimizations

### **Phase 4: Polish & Testing (Week 4)**
1. ✅ UI/UX improvements
2. ✅ Performance optimizations
3. ✅ Comprehensive testing
4. ✅ Documentation updates

---

## 🎯 Kết Luận

Việc refactor tính năng Streak sẽ mang lại:

- **🔧 Maintainability**: Dễ bảo trì và mở rộng
- **⚡ Performance**: Tối ưu hóa hiệu suất
- **🛡️ Reliability**: Xử lý lỗi tốt hơn
- **🎨 Flexibility**: Dễ dàng thay đổi cấu hình
- **📊 Scalability**: Có thể scale cho nhiều user

Ưu tiên thực hiện theo từng phase để đảm bảo tính ổn định của hệ thống hiện tại.
