# Công <PERSON><PERSON> Dụng

## Kiến Trúc
- **Frontend**: Next.js 15 với App Router (React 19)
- **Backend**: Strapi 5 (Headless CMS)
- **Cơ Sở Dữ Liệu**: MySQL
- **X<PERSON><PERSON>**: JWT với cookies, Google OAuth
- **<PERSON><PERSON>**: <PERSON><PERSON><PERSON> hợp <PERSON>OS
- **Email**: <PERSON>hà cung cấp <PERSON>vo (SendinBlue)

## Stack Frontend
- **Framework**: Next.js 15 với App Router
- **Thư Viện UI**: React 19 với functional components và hooks
- **Styling**: Tailwind CSS với hệ thống thiết kế tùy chỉnh
- **HTTP Client**: Axios với interceptors cho xác thực
- **Quản Lý State**: React Context (AuthContext, UserProvider, NotificationContext)
- **<PERSON><PERSON><PERSON>**: @react-oauth/google, react-cookie, jwt-decode
- **UI Components**: react-hot-toast, react-icons, react-spinners, react-select
- **Video**: hls.js cho video streaming
- **Carousel**: Swiper.js

## Stack Backend
- **CMS**: Strapi 5 với TypeScript
- **Cơ Sở Dữ Liệu**: MySQL với Knex.js query builder
- **Xác Thực**: JWT, users-permissions plugin, strapi-plugin-sso
- **Email**: strapi-provider-email-brevo
- **Lưu Trữ File**: Upload local với tối ưu hóa hình ảnh
- **Phiên Bản Node**: >=18.0.0 <=22.x.x

## Lệnh Phát Triển

### Frontend (Next.js)
```bash
cd frontend
npm install          # Cài đặt dependencies
npm run dev         # Khởi động development server (http://localhost:3000)
npm run build       # Build cho production
npm run start       # Khởi động production server
npm run lint        # Chạy ESLint
```

### Backend (Strapi)
```bash
cd backend
npm install          # Cài đặt dependencies
npm run develop     # Khởi động development server (http://localhost:1337)
npm run build       # Build admin panel và server
npm run start       # Khởi động production server
npm run seed:example # Seed database với dữ liệu mẫu
```

## Thiết Lập Môi Trường
- Frontend yêu cầu `NEXT_PUBLIC_STRAPI_API_URL` và thông tin đăng nhập Google OAuth
- Backend yêu cầu thông tin đăng nhập database, JWT secrets, PayOS keys và Brevo API key
- Cả hai môi trường sử dụng file `.env` (xem file `.env.example`)

## Build & Triển Khai
- **Frontend**: Tối ưu hóa cho triển khai Vercel với tối ưu hóa hình ảnh
- **Backend**: Triển khai tự động với Dokploy khi merge vào nhánh main
- **Database**: MySQL với hỗ trợ SSL cho production
- **CI/CD**: Tự động deploy khi merge vào nhánh main, không cần thao tác thủ công

## Quy Trình Deployment
1. Phát triển trên nhánh feature/development
2. Tạo Pull Request để review code
3. Merge vào nhánh main
4. Dokploy tự động phát hiện thay đổi và deploy
5. Không cần thao tác thủ công nào khác