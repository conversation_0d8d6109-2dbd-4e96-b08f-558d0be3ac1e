# Cấu Trúc Dự Án

## Tổ Chức Thư <PERSON>
```
/
├── frontend/          # Ứng dụng Next.js
├── backend/           # Strapi CMS
└── *.sql             # Database dumps/backups
```

## Cấu Trúc Frontend (Next.js App Router)
```
frontend/
├── app/                    # App Router pages và layouts
│   ├── api/               # API routes và client wrappers
│   │   ├── auth/          # Authentication endpoints
│   │   └── strapi.js      # Axios client cho Strapi API
│   ├── (routes)/          # Page routes (URLs tiếng Việt)
│   │   ├── dang-nhap/     # Trang đăng nhập
│   │   ├── dang-ky/       # Trang đăng ký
│   │   ├── khoa-hoc/      # Trang khóa học
│   │   ├── quan-ly/       # Dashboard học viên
│   │   └── ...
│   ├── layout.js          # Root layout
│   ├── page.js           # Trang chủ
│   └── providers.js      # Context providers
├── components/            # React components tái sử dụng
│   ├── dashboard/        # Components dành cho dashboard
│   ├── icons/           # Custom icon components
│   └── layouts/         # Layout components
├── context/             # React Context providers
├── hooks/              # Custom React hooks
├── utils/              # Utility functions
├── data/               # Dữ liệu tĩnh (trường học, v.v.)
└── public/             # Static assets
```

## Cấu Trúc Backend (Strapi)
```
backend/
├── src/
│   ├── api/                    # API endpoints theo entity
│   │   ├── course/            # Quản lý khóa học
│   │   │   ├── content-types/ # Data models
│   │   │   ├── controllers/   # Request handlers
│   │   │   ├── routes/        # Route definitions
│   │   │   └── services/      # Business logic
│   │   ├── auth/              # Custom auth endpoints
│   │   ├── payment/           # Xử lý thanh toán
│   │   └── ...
│   ├── components/            # Strapi components tái sử dụng
│   ├── email-templates/       # HTML email templates
│   └── extensions/           # Plugin customizations
├── config/                   # Cấu hình Strapi
│   ├── database.ts          # Kết nối database
│   ├── middlewares.ts       # CORS, security settings
│   └── plugins.ts           # Cấu hình plugin
└── public/                  # Uploaded files và assets
```

## Quy Ước Đặt Tên

### Files & Folders
- **Frontend**: camelCase cho components, kebab-case cho pages
- **Backend**: kebab-case cho API folders, camelCase cho files
- **URLs**: Tiếng Việt với dấu gạch ngang (ví dụ: `/dang-nhap`, `/khoa-hoc`)

### Components
- React components sử dụng PascalCase (ví dụ: `LoginForm.jsx`)
- Custom hooks sử dụng camelCase với tiền tố `use` (ví dụ: `useScreenSize.jsx`)
- Context providers kết thúc bằng `Context` hoặc `Provider`

### Cấu Trúc API
- Strapi tuân theo quy ước RESTful với custom endpoints cho các thao tác phức tạp
- Authentication routes có tiền tố `/auth/`
- Payment routes có tiền tố `/payment/`

## Mẫu Kiến Trúc Chính

### Frontend
- **App Router**: File-based routing với layouts và nested routes
- **Context Pattern**: Quản lý global state cho auth, user data, notifications
- **Component Composition**: UI components tái sử dụng với props
- **Custom Hooks**: Trích xuất logic dùng chung (screen size, API calls)

### Backend
- **Headless CMS**: Quản lý nội dung tách biệt khỏi presentation
- **Plugin Architecture**: Chức năng mở rộng thông qua Strapi plugins
- **Service Layer**: Business logic tách biệt khỏi controllers
- **Content Types**: Data models có cấu trúc với relationships

## File Cấu Hình
- `next.config.mjs`: Cấu hình Next.js với tối ưu hóa hình ảnh
- `tailwind.config.js`: Hệ thống thiết kế tùy chỉnh với brand colors và spacing
- `middleware.js`: Authentication và routing middleware
- `tsconfig.json`: Cấu hình TypeScript cho backend