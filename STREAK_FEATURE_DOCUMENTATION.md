# 🔥 Tính Năng Streak - Ông Ba Dạy Hóa

## 📋 Tổng Quan

Tính năng Streak là một hệ thống học tập hàng ngày được thiết kế để khuyến khích học sinh duy trì thói quen học tập đều đặn. Mỗi ngày, học sinh sẽ hoàn thành một bộ câu hỏi để duy trì streak của mình.

## 🏗️ Kiến Trúc Hệ Thống

### Database Schema

#### 1. **streak_questions** - Định nghĩa streak hàng ngày
```json
{
  "id": "integer",
  "description": "text",
  "value": "date",           // Ngày của streak
  "course_id": "integer"    // Liên kết với khóa học
}
```

#### 2. **streaks** - Tracking tiến độ user
```json
{
  "id": "integer",
  "user_id": "integer",
  "streak_question_id": "integer",
  "isJoin": "boolean",      // true = hoàn thành, false = chưa hoàn thành
  "time": "integer",        // Thời gian làm bài (giây)
  "document_id": "string"   // Strapi document ID
}
```

#### 3. **questions** - Câu hỏi trong streak
```json
{
  "id": "integer",
  "content": "text",
  "A": "string", "B": "string", "C": "string", "D": "string",
  "correct_answer": "string",
  "explain": "text",
  "type": "enum[TN_4, TN_2, TN_value]",
  "question_status": "enum[todo, checking, approval, reject]",
  "exercise_type_id": "integer",    // Liên kết với độ khó
  "streak_question_id": "integer"   // Liên kết với streak
}
```

#### 4. **questions_answers** - Câu trả lời của user
```json
{
  "id": "integer",
  "answer": "string",
  "is_correct": "boolean",
  "is_star_point": "boolean",       // Có sử dụng star point không
  "user_id": "integer",
  "streak_question_id": "integer",
  "question_id": "integer"
}
```

#### 5. **exercise_types** - Phân loại độ khó
```json
{
  "id": "integer",
  "name": "string",
  "type": "enum[biet, hieu, van_dung, van_dung_cao]",
  "point": "integer"        // Điểm số cho mỗi loại
}
```

## 🎯 Flow Chính Của Tính Năng

### 1. **Khởi Tạo Streak Hàng Ngày**

```javascript
// 1. Lấy streak question cho ngày hiện tại
const getStreakQuestion = async () => {
  const date = new Date();
  const formatted = `${yyyy}-${mm}-${dd}`;
  const orders = await strapi.orders.getOrderByUser(user.id, 'course');
  const res = await strapi.streak.getStreak(formatted, orders.data[0].course.id);
  
  if (res?.data?.data.length > 0) {
    setStreakId(res.data.data[0].id);
  } else {
    // Hiển thị thông báo không tìm thấy streak
  }
}

// 2. Tạo streak session cho user
const clickStart = async () => {
  const data = {
    isJoin: 0,              // Chưa hoàn thành
    user_id: user.id,
    streak_question_id: streakId
  }
  const res = await strapi.streak.createStreak(data);
  // Bắt đầu timer và load câu hỏi
}
```

### 2. **Cấu Trúc Câu Hỏi**

Mỗi streak có **3 câu hỏi** với độ khó tăng dần:

- **Câu 1** (index 0): Dễ - exercise_type "biet" hoặc "hieu"
- **Câu 2** (index 1): Trung bình - exercise_type "van_dung"  
- **Câu 3** (index 2): Khó - exercise_type "van_dung_cao" (**TÙY CHỌN**)

### 3. **Flow Làm Bài Chi Tiết**

#### **Bước 1-2: Làm 2 câu bắt buộc**
```javascript
const ViewAnswerAndNextQuestion = async () => {
  if (!isViewAnswer) {
    // Lưu câu trả lời
    if (!userAnswer.answer) {
      toast.error('Điền đáp án đã nhé!');
      return;
    }
    await strapi.streak.saveQuestionAnswer(userAnswer);
    setIsViewAnswer(true);
  } else {
    let i = indexQuestionActive + 1;
    
    if (i === 2) {
      // Sau câu 2 -> Hiển thị màn hình lựa chọn
      setIsQuestion2(true);
      setIsViewAnswer(false);
    } else {
      // Chuyển câu tiếp theo
      setIndexQuestionActive(i);
      setQuestionActive(questions[i]);
    }
  }
}
```

#### **Bước 3: Màn hình lựa chọn (sau câu 2)**
```javascript
// UI hiển thị 2 options:
{
  !isQuestion2 ? 
    <QuestionComponent />  // Câu hỏi bình thường
    :
    <ChoiceScreen>
      <p>Tiếp theo là <strong>câu hỏi khó</strong> á nha. Tự tin thử sức đi!</p>
      
      <button onClick={finishStreak}>
        Hoàn thành streak
      </button>
      
      <button onClick={handelContinueStreak}>
        Thử sức
      </button>
    </ChoiceScreen>
}
```

#### **Option A: Hoàn thành streak**
```javascript
const finishStreak = async () => {
  const d = {
    documentId: dayActive.documentId, 
    isJoin: true,     // Đánh dấu hoàn thành
    time: time
  };
  await strapi.streak.finishStreak(d);
  setIsFinish(true);
  setIsStart(false);
}
```

#### **Option B: Thử sức (làm câu 3)**
```javascript
const handelContinueStreak = () => {
  setIsQuestion2(false);        // Tắt màn hình lựa chọn
  setIndexQuestionActive(2);    // Chuyển đến câu 3
  setQuestionActive(questions[2]); // Load câu hỏi khó
}
```

## ⭐ Hệ Thống Star Point

### **Cơ Chế Hoạt Động**
- User có thể sử dụng **1 lần duy nhất** trong mỗi streak
- **Nhân đôi điểm số** của câu hỏi đó
- Chỉ có thể sử dụng **trước khi xem kết quả**

### **UI/UX Flow**
```javascript
// 1. Button star point trên mỗi câu hỏi
<button onClick={useStarPoint}>
  {userAnswer.is_star_point ? 
    "Đã đặt sao hy vọng" : 
    "Đặt sao hy vọng"
  }
</button>

// 2. Modal xác nhận
const useStarPoint = () => {
  if (userAnswer.is_star_point) {
    // Hủy star point
    setUserAnswer({...userAnswer, is_star_point: false});
  } else {
    // Hiển thị modal xác nhận
    setIsViewStarPoint(true);
  }
}

// 3. Logic tính điểm
const finalPoint = userAnswer.is_star_point ? 
  questionActive.exercise_type.point * 2 : 
  questionActive.exercise_type.point;
```

### **Backend Tính Điểm**
```sql
-- Query tính tổng điểm với star point
SELECT sum(
  CASE WHEN is_star_point = 1 
    THEN point * 2 
    ELSE point 
  END
) as total_point
FROM questions_answers qa
JOIN exercise_types et ON qa.question_id = et.question_id
WHERE qa.user_id = ? AND qa.streak_question_id = ?
```

## ⏱️ Hệ Thống Timer

### **Logic Tracking Thời Gian**
```javascript
useEffect(() => {
  if (isQuestion2) {
    // Tạm dừng timer khi ở màn hình lựa chọn
    setTime(time);
  } else if (isStart && dayActive) {
    // Bắt đầu timer
    let t = setInterval(() => {
      setTime(time + 1);
      
      // Auto-save mỗi 10 giây
      if (time % 10 === 0) {
        strapi.streak.updateTime({
          documentId: dayActive.documentId, 
          time: time
        });
      }
    }, 1000);
    setTimer(t);
  }
}, [isStart, time, isQuestion2]);
```

### **Tính Năng Resume**
```javascript
// Khi user quay lại, hệ thống tự động resume
const getQuestionByStreak = async () => {
  const res = await strapi.streak.getQuestionByStreak(streakId);
  setQuestions(res.data.data);
  
  // Kiểm tra câu trả lời đã có
  const answers = await strapi.streak.getAnswerStreakByUser({
    user: user.id, 
    streakId: streakId
  });
  
  if (answers.data.length > 0) {
    // Resume từ vị trí đã làm
    if (answers.data.length === 2) {
      setIsQuestion2(true);           // Hiển thị màn lựa chọn
      setIndexQuestionActive(2);
    } else if (answers.data.length === 3) {
      setIsStart(false);              // Đã hoàn thành
    } else {
      setIndexQuestionActive(answers.data.length);
    }
  }
}
```

## 📊 Hệ Thống Điểm Số

### **Phân Loại Độ Khó và Điểm**
| Loại | Tên | Điểm Cơ Bản | Mô Tả |
|------|-----|-------------|-------|
| `biet` | Biết | 10 | Câu hỏi cơ bản, nhớ kiến thức |
| `hieu` | Hiểu | 15 | Câu hỏi hiểu khái niệm |
| `van_dung` | Vận dụng | 20 | Câu hỏi áp dụng công thức |
| `van_dung_cao` | Vận dụng cao | 30 | Câu hỏi phức tạp, tổng hợp |

### **Công Thức Tính Điểm**
```javascript
// Điểm cho mỗi câu
const questionPoint = is_star_point ?
  exercise_type.point * 2 :
  exercise_type.point;

// Tổng điểm streak
const totalPoint = questions.reduce((sum, q) => {
  return sum + (q.is_correct ? questionPoint : 0);
}, 0);
```

## 🔄 State Management

### **Các State Quan Trọng**
```javascript
// Trạng thái câu hỏi
const [indexQuestionActive, setIndexQuestionActive] = useState(0); // 0,1,2
const [questions, setQuestions] = useState([]);                    // 3 câu hỏi
const [questionActive, setQuestionActive] = useState(null);        // Câu hiện tại

// Trạng thái UI
const [isStart, setIsStart] = useState(false);          // Đã bắt đầu
const [isFinish, setIsFinish] = useState(false);        // Đã hoàn thành
const [isQuestion2, setIsQuestion2] = useState(false);  // Màn hình lựa chọn
const [isViewAnswer, setIsViewAnswer] = useState(false); // Hiển thị kết quả

// Trạng thái star point
const [isUseStarPoint, setIsUseStarPoint] = useState(false);  // Đã dùng star point
const [isViewStarPoint, setIsViewStarPoint] = useState(false); // Modal star point

// Dữ liệu user
const [userAnswer, setUserAnswer] = useState({
  answer: "",
  is_correct: false,
  is_star_point: false
});

// Timer
const [time, setTime] = useState(0);
const [timer, setTimer] = useState(null);
```

## 🔍 API Endpoints

### **Frontend API Calls**
```javascript
// 1. Lấy streak question theo ngày
strapi.streak.getStreak(date, courseId)

// 2. Lấy câu hỏi của streak
strapi.streak.getQuestionByStreak(streakId)

// 3. Tạo streak session
strapi.streak.createStreak({
  isJoin: 0,
  user_id: userId,
  streak_question_id: streakId
})

// 4. Lưu câu trả lời
strapi.streak.saveQuestionAnswer({
  answer: "A",
  is_correct: true,
  is_star_point: false,
  user: userId,
  streak_question_id: streakId,
  question: questionId
})

// 5. Cập nhật thời gian
strapi.streak.updateTime({
  documentId: streakDocumentId,
  time: timeInSeconds
})

// 6. Hoàn thành streak
strapi.streak.finishStreak({
  documentId: streakDocumentId,
  isJoin: true,
  time: finalTime
})

// 7. Lấy câu trả lời đã có (resume)
strapi.streak.getAnswerStreakByUser({
  user: userId,
  streakId: streakId
})

// 8. Lấy dữ liệu hoàn thành
strapi.streak.getDataFinish({
  user_id: userId,
  streak_question_id: streakId
})
```

### **Backend Controllers**
```javascript
// 1. Lấy streak data theo user (tuần hiện tại)
async getStreakByUser(ctx) {
  // Complex SQL query để lấy data 7 ngày trong tuần
  // Bao gồm: dateInWeek, documentId, time, isJoin, isActive
}

// 2. Tính tổng rollup streak
async getTotalRollup(ctx) {
  // Đếm số ngày liên tiếp user hoàn thành streak
}

// 3. Lấy dữ liệu kết thúc streak
async getDataFinish(ctx) {
  // Tính tổng: count, is_correct, point (có star point x2)
  // Bao gồm max streak của user
}
```

## 🎨 UI/UX Components

### **Màn Hình Chính**
- **Progress Bar**: Hiển thị tiến độ (1/3, 2/3, 3/3)
- **Timer**: Đếm thời gian làm bài
- **Question Content**: Nội dung câu hỏi + hình ảnh (nếu có)
- **Answer Options**: 4 đáp án A, B, C, D
- **Star Point Button**: Đặt sao hy vọng
- **Action Button**: "Xem kết quả" / "Câu tiếp theo" / "Hoàn thành"

### **Màn Hình Lựa Chọn (sau câu 2)**
```jsx
<div className="choice_screen">
  <h3>Tiếp theo là <strong>câu hỏi khó</strong> á nha</h3>
  <p>Tự tin thử sức đi, Ba tin tụ bây! Đã chơi rồi mà thoát là công sức HAI câu trước tao không tính cho tụ bây đâu</p>

  <div className="buttons">
    <button onClick={finishStreak} className="secondary">
      Hoàn thành streak
    </button>
    <button onClick={handelContinueStreak} className="primary">
      Thử sức
    </button>
  </div>
</div>
```

### **Star Point Modal**
```jsx
<StarPointPopup>
  <h3>Mại dzô, đặt sao hy vọng đi nè!</h3>
  <p>Sao hy vọng giúp bạn x2 số điểm của câu hỏi này và bạn chỉ được sử dụng 1 LẦN duy nhất trong streak hôm nay thôi nhé</p>

  <button onClick={onClose}>Hủy bỏ</button>
  <button onClick={handleUse}>Sử dụng</button>
</StarPointPopup>
```

## 🚨 Error Handling & Edge Cases

### **1. Không Tìm Thấy Streak**
```javascript
if (!streakData || streakData.length === 0) {
  showNotification({
    type: "error",
    title: "Streak không tìm thấy",
    message: "Nhắn cho Ba để thêm thông tin streak nhé bây!"
  });
}
```

### **2. User Chưa Chọn Đáp Án**
```javascript
if (!userAnswer.answer) {
  toast.error('Điền đáp án đã nhé!');
  return;
}
```

### **3. Resume Streak**
- Kiểm tra số câu đã trả lời
- Restore timer từ database
- Set đúng indexQuestionActive
- Hiển thị màn hình phù hợp

### **4. Network Error**
- Retry mechanism cho API calls
- Local storage backup cho timer
- Graceful degradation

## 📈 Analytics & Tracking

### **Dữ Liệu Được Track**
1. **Thời gian làm bài**: Từng câu và tổng thể
2. **Tỷ lệ đúng/sai**: Theo từng loại câu hỏi
3. **Sử dụng star point**: Tần suất và hiệu quả
4. **Lựa chọn skip câu 3**: Tỷ lệ user chọn "Thử sức"
5. **Streak liên tiếp**: Số ngày duy trì streak

### **Reports**
```sql
-- Báo cáo hiệu suất user
SELECT
  u.username,
  COUNT(*) as total_streaks,
  AVG(s.time) as avg_time,
  SUM(CASE WHEN s.isJoin = 1 THEN 1 ELSE 0 END) as completed_streaks
FROM streaks s
JOIN users u ON s.user_id = u.id
GROUP BY u.id;
```

## 🔧 Cách Tạo Streak Mới

### **1. Tạo Streak Question**
```sql
INSERT INTO streak_questions (description, value, course_id)
VALUES ('Streak ngày 26/07/2024', '2024-07-26', 1);
```

### **2. Tạo 3 Câu Hỏi**
```sql
-- Câu dễ (10 điểm)
INSERT INTO questions (content, A, B, C, D, correct_answer, explain, type, question_status, exercise_type_id, streak_question_id)
VALUES ('Câu hỏi dễ...', 'A', 'B', 'C', 'D', 'A', 'Giải thích...', 'TN_4', 'approval', 1, @streak_id);

-- Câu trung bình (20 điểm)
INSERT INTO questions (content, A, B, C, D, correct_answer, explain, type, question_status, exercise_type_id, streak_question_id)
VALUES ('Câu hỏi trung bình...', 'A', 'B', 'C', 'D', 'B', 'Giải thích...', 'TN_4', 'approval', 2, @streak_id);

-- Câu khó (30 điểm)
INSERT INTO questions (content, A, B, C, D, correct_answer, explain, type, question_status, exercise_type_id, streak_question_id)
VALUES ('Câu hỏi khó...', 'A', 'B', 'C', 'D', 'C', 'Giải thích...', 'TN_4', 'approval', 3, @streak_id);
```

## 🎯 Use Cases Chính

### **Use Case 1: User làm streak lần đầu**
1. User vào trang streak
2. Hệ thống load streak question cho ngày hiện tại
3. User click "Bắt đầu"
4. Làm câu 1 → câu 2 → màn hình lựa chọn
5. Chọn "Hoàn thành" hoặc "Thử sức"

### **Use Case 2: User resume streak**
1. User đã làm 1-2 câu trước đó
2. Hệ thống tự động resume từ vị trí đã làm
3. Timer tiếp tục từ thời điểm đã lưu
4. Hiển thị đúng màn hình (câu hỏi hoặc lựa chọn)

### **Use Case 3: User sử dụng star point**
1. User click "Đặt sao hy vọng" trước khi trả lời
2. Hiển thị modal xác nhận
3. User confirm → điểm x2 cho câu đó
4. Disable star point cho các câu còn lại

### **Use Case 4: User skip câu khó**
1. Hoàn thành 2 câu đầu
2. Chọn "Hoàn thành streak"
3. Hệ thống tính điểm 2 câu + mark streak completed
4. Hiển thị kết quả và thống kê

---

## 🎉 Kết Luận

Tính năng Streak được thiết kế để:
- **Khuyến khích học tập đều đặn** qua gamification
- **Linh hoạt** cho phép user lựa chọn mức độ thử thách
- **Công bằng** với hệ thống điểm số rõ ràng
- **Engaging** với star point và timer
- **Robust** với khả năng resume và error handling

Hệ thống này tạo ra trải nghiệm học tập tích cực, giúp học sinh duy trì động lực học tập lâu dài.
