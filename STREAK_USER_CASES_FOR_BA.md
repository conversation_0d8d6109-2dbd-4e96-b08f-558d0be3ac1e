# 📋 User Cases - <PERSON><PERSON><PERSON> Năng Streak (Dành cho BA)

## 🎯 Tổng Quan

Tài liệu này mô tả chi tiết tất cả các trường hợp (use cases) và scenarios có thể xảy ra khi user tham gia tính năng Streak hàng ngày.

## 👤 User Personas

### **Primary User**: <PERSON><PERSON><PERSON> sinh THPT
- Đã mua khóa học
- C<PERSON> tài khoản đăng nhập
- Muốn duy trì thói quen học tập hàng ngày

### **Secondary User**: Học sinh thử nghiệm
- Chưa mua khóa học hoặc hết hạn
- <PERSON><PERSON> thể gặp hạn chế truy cập

## 🚀 User Journey Overview

```
User vào trang Streak → Kiểm tra streak hôm nay → Bắt đầu làm bài → 
Làm 2 câ<PERSON> b<PERSON><PERSON> buộ<PERSON> → Lựa chọn làm câu 3 → <PERSON><PERSON><PERSON> thành → <PERSON>em kết quả
```

---

## 📝 Chi Tiết User Cases

### **UC-01: User lần đầu vào trang Streak**

#### **Preconditions:**
- User đã đăng nhập
- User có khóa học active
- Có streak question cho ngày hôm nay

#### **Main Flow:**
1. User click vào menu "Streak"
2. Hệ thống load trang streak
3. Hiển thị thông tin streak tuần này (7 ngày)
4. Hiển thị streak question cho ngày hôm nay
5. User thấy button "Bắt đầu"

#### **Expected Result:**
- Trang streak load thành công
- Hiển thị đúng ngày active (hôm nay)
- Button "Bắt đầu" có thể click

#### **Alternative Flows:**
- **AF-01a**: Không có streak cho ngày hôm nay
  - Hiển thị thông báo: "Streak không tìm thấy"
  - Message: "Nhắn cho Ba để thêm thông tin streak nhé bây!"
  
- **AF-01b**: User chưa mua khóa học
  - Redirect đến trang mua khóa học
  - Hoặc hiển thị thông báo hạn chế

---

### **UC-02: User bắt đầu streak mới**

#### **Preconditions:**
- User chưa làm streak hôm nay
- Có 3 câu hỏi sẵn sàng

#### **Main Flow:**
1. User click "Bắt đầu"
2. Hệ thống tạo streak session
3. Bắt đầu timer đếm thời gian
4. Hiển thị câu hỏi đầu tiên (1/3)
5. User thấy progress bar, timer, và nội dung câu hỏi

#### **Expected Result:**
- Timer bắt đầu chạy
- Hiển thị "Câu 1" với progress 33%
- 4 đáp án A, B, C, D
- Button "Đặt sao hy vọng" available

#### **Alternative Flows:**
- **AF-02a**: Lỗi tạo streak session
  - Hiển thị thông báo lỗi
  - User có thể thử lại

---

### **UC-03: User trả lời câu hỏi**

#### **Preconditions:**
- User đang trong streak session
- Câu hỏi đã được load

#### **Main Flow:**
1. User chọn 1 trong 4 đáp án (A, B, C, D)
2. User có thể click "Đặt sao hy vọng" (optional)
3. User click "Xem kết quả"
4. Hệ thống lưu câu trả lời
5. Hiển thị kết quả đúng/sai + giải thích
6. Hiển thị điểm được cộng
7. User click "Câu tiếp theo"

#### **Expected Result:**
- Câu trả lời được lưu thành công
- Hiển thị feedback ngay lập tức
- Timer tiếp tục chạy
- Chuyển sang câu tiếp theo

#### **Alternative Flows:**
- **AF-03a**: User chưa chọn đáp án
  - Hiển thị toast: "Điền đáp án đã nhé!"
  - Không cho phép tiếp tục

- **AF-03b**: User sử dụng sao hy vọng
  - Hiển thị modal xác nhận
  - Điểm số x2 nếu đúng
  - Disable sao hy vọng cho các câu còn lại

- **AF-03c**: Lỗi network khi lưu
  - Hiển thị thông báo lỗi
  - Cho phép thử lại
  - Timer tạm dừng

---

### **UC-04: User hoàn thành 2 câu bắt buộc**

#### **Preconditions:**
- User đã trả lời 2 câu đầu
- Đang ở câu thứ 2

#### **Main Flow:**
1. User hoàn thành câu 2
2. Hệ thống hiển thị màn hình lựa chọn
3. Timer tạm dừng
4. Hiển thị 2 options:
   - "Hoàn thành streak" (secondary button)
   - "Thử sức" (primary button)
5. User chọn 1 trong 2 options

#### **Expected Result:**
- Màn hình lựa chọn hiển thị đúng
- Timer dừng lại
- 2 buttons hoạt động bình thường

#### **UI Content:**
- **Title**: "Tiếp theo là câu hỏi khó á nha"
- **Description**: "Tự tin thử sức đi, Ba tin tụ bây! Đã chơi rồi mà thoát là công sức HAI câu trước tao không tính cho tụ bây đâu"

---

### **UC-05: User chọn "Hoàn thành streak"**

#### **Preconditions:**
- User ở màn hình lựa chọn
- Đã hoàn thành 2 câu bắt buộc

#### **Main Flow:**
1. User click "Hoàn thành streak"
2. Hệ thống mark streak là completed (isJoin = true)
3. Lưu thời gian hoàn thành
4. Hiển thị màn hình kết quả
5. Tính tổng điểm từ 2 câu đã làm
6. Cập nhật streak count của user

#### **Expected Result:**
- Streak được đánh dấu hoàn thành
- Hiển thị tổng điểm, thời gian
- Streak count tăng lên
- Có thể xem thống kê

#### **Result Screen Content:**
- Tổng số câu: 2/3
- Số câu đúng: X/2
- Tổng điểm: XXX
- Thời gian: XX:XX
- Streak hiện tại: X ngày

---

### **UC-06: User chọn "Thử sức" (làm câu 3)**

#### **Preconditions:**
- User ở màn hình lựa chọn
- Sẵn sàng thử thách bản thân

#### **Main Flow:**
1. User click "Thử sức"
2. Hệ thống load câu hỏi thứ 3 (khó nhất)
3. Timer tiếp tục chạy
4. Hiển thị "Câu 3" với progress 100%
5. User trả lời như bình thường
6. Sau khi xem kết quả → "Hoàn thành"
7. Hiển thị kết quả cuối cùng với 3 câu

#### **Expected Result:**
- Load câu 3 thành công
- Timer resume từ thời điểm dừng
- Có thể sử dụng sao hy vọng (nếu chưa dùng)
- Kết quả tính cả 3 câu

---

### **UC-07: User resume streak đã làm dở**

#### **Preconditions:**
- User đã bắt đầu streak hôm nay
- Chưa hoàn thành (thoát giữa chừng)

#### **Main Flow:**
1. User vào lại trang streak
2. Hệ thống detect streak đang dở dang
3. Auto-resume từ vị trí đã làm:
   - Nếu làm 1 câu → tiếp tục câu 2
   - Nếu làm 2 câu → hiển thị màn hình lựa chọn
   - Nếu làm 3 câu → hiển thị kết quả
4. Timer resume từ thời gian đã lưu

#### **Expected Result:**
- Resume đúng vị trí
- Timer hiển thị thời gian tích lũy
- Không mất dữ liệu đã làm
- Sao hy vọng status được preserve

#### **Resume Scenarios:**
- **Scenario A**: Làm dở câu 1
  - Resume tại câu 1, chưa submit
  - Timer từ thời điểm dừng
  
- **Scenario B**: Hoàn thành 1 câu
  - Resume tại câu 2
  - Hiển thị kết quả câu 1 đã làm
  
- **Scenario C**: Hoàn thành 2 câu
  - Resume tại màn hình lựa chọn
  - Timer dừng như cũ

---

### **UC-08: User đã hoàn thành streak hôm nay**

#### **Preconditions:**
- User đã complete streak hôm nay
- isJoin = true trong database

#### **Main Flow:**
1. User vào trang streak
2. Hệ thống detect đã hoàn thành
3. Hiển thị màn hình kết quả cuối cùng
4. Không cho phép làm lại
5. Hiển thị thống kê và khuyến khích

#### **Expected Result:**
- Không có button "Bắt đầu"
- Hiển thị kết quả đã đạt được
- Khuyến khích quay lại ngày mai
- Có thể xem streak history

#### **Result Display:**
- Badge "Đã hoàn thành"
- Kết quả chi tiết
- Streak count hiện tại
- Message động viên

---

### **UC-09: Sử dụng Star Point (Sao hy vọng)**

#### **Preconditions:**
- User chưa sử dụng sao hy vọng trong streak hôm nay
- Đang ở bước chọn đáp án (chưa submit)

#### **Main Flow:**
1. User click "Đặt sao hy vọng"
2. Hiển thị modal xác nhận
3. User đọc thông tin về sao hy vọng
4. User click "Sử dụng" hoặc "Hủy bỏ"
5. Nếu sử dụng: button chuyển thành "Đã đặt sao hy vọng"
6. Khi submit: điểm số x2 nếu đúng

#### **Expected Result:**
- Modal hiển thị đúng thông tin
- Button state thay đổi
- Điểm số được nhân đôi
- Disable cho các câu còn lại

#### **Modal Content:**
- **Title**: "Mại dzô, đặt sao hy vọng đi nè!"
- **Description**: "Sao hy vọng giúp bạn x2 số điểm của câu hỏi này và bạn chỉ được sử dụng 1 LẦN duy nhất trong streak hôm nay thôi nhé"
- **Buttons**: "Hủy bỏ" / "Sử dụng"

#### **Alternative Flows:**
- **AF-09a**: User đã sử dụng sao hy vọng
  - Button bị disable
  - Hiển thị "Đã sử dụng sao hy vọng"

---

### **UC-10: Error Handling Cases**

#### **Case 10.1: Network Error**
- **Trigger**: Mất kết nối internet
- **Behavior**: 
  - Hiển thị thông báo lỗi
  - Timer tạm dừng
  - Cho phép retry
  - Lưu state local nếu có thể

#### **Case 10.2: Session Timeout**
- **Trigger**: User idle quá lâu
- **Behavior**:
  - Auto-save progress
  - Thông báo session hết hạn
  - Redirect về login

#### **Case 10.3: Server Error**
- **Trigger**: Backend trả về 500
- **Behavior**:
  - Thông báo lỗi hệ thống
  - Khuyến khích thử lại sau
  - Log error cho dev

#### **Case 10.4: Data Corruption**
- **Trigger**: Dữ liệu không nhất quán
- **Behavior**:
  - Reset streak session
  - Thông báo cho user
  - Cho phép bắt đầu lại

---

### **UC-11: Edge Cases**

#### **Case 11.1: User làm streak vào 23:59**
- **Scenario**: Bắt đầu trước 00:00, hoàn thành sau 00:00
- **Expected**: Vẫn tính cho ngày hôm trước
- **Implementation**: Dựa vào streak_question_id, không phải thời gian hoàn thành

#### **Case 11.2: Multiple tabs/devices**
- **Scenario**: User mở streak trên nhiều tab/thiết bị
- **Expected**: Sync state giữa các tab
- **Implementation**: Real-time sync hoặc warning

#### **Case 11.3: Browser refresh giữa chừng**
- **Scenario**: User refresh trang khi đang làm
- **Expected**: Resume từ vị trí đã lưu
- **Implementation**: Auto-save mỗi 10 giây

#### **Case 11.4: Thay đổi múi giờ**
- **Scenario**: User travel sang múi giờ khác
- **Expected**: Streak vẫn theo múi giờ server
- **Implementation**: Sử dụng UTC time

---

## 📊 Business Rules

### **Scoring Rules:**
- Câu dễ: 10-15 điểm
- Câu trung bình: 20 điểm  
- Câu khó: 30 điểm
- Sao hy vọng: x2 điểm số
- Chỉ được dùng 1 sao hy vọng/streak

### **Completion Rules:**
- Tối thiểu 2 câu để complete streak
- Câu thứ 3 là optional
- Timer không giới hạn (nhưng được track)
- Có thể resume bất cứ lúc nào trong ngày

### **Streak Count Rules:**
- +1 streak khi isJoin = true
- Reset về 0 nếu miss 1 ngày
- Không tính ngày chưa có streak question

---

## 🎯 Success Metrics

### **User Engagement:**
- Daily Active Users làm streak
- Completion rate (2 câu vs 3 câu)
- Star point usage rate
- Average time per streak

### **Learning Outcomes:**
- Accuracy rate theo từng độ khó
- Improvement over time
- Streak retention (7 days, 30 days)

### **Technical Metrics:**
- Page load time
- API response time
- Error rate
- Resume success rate

---

## 🚨 Risk Scenarios

### **High Risk:**
- Server down trong giờ cao điểm
- Database corruption
- Payment system integration issues

### **Medium Risk:**
- Network instability
- Browser compatibility
- Mobile responsive issues

### **Low Risk:**
- UI text changes
- Minor bug fixes
- Performance optimization

---

## 📋 Testing Checklist for QA

### **Functional Testing:**
- [ ] All user flows work end-to-end
- [ ] Resume functionality works correctly
- [ ] Star point system works as expected
- [ ] Scoring calculation is accurate
- [ ] Timer functions properly

### **Edge Case Testing:**
- [ ] Multiple tabs scenario
- [ ] Network interruption
- [ ] Browser refresh
- [ ] Timezone changes
- [ ] Midnight boundary

### **Performance Testing:**
- [ ] Page load under 3 seconds
- [ ] API calls under 1 second
- [ ] Memory usage acceptable
- [ ] No memory leaks

### **Security Testing:**
- [ ] User can only access own data
- [ ] No SQL injection vulnerabilities
- [ ] Session management secure
- [ ] Input validation proper

---

*Tài liệu này cung cấp đầy đủ thông tin để BA hiểu rõ tất cả scenarios có thể xảy ra trong tính năng Streak và lập kế hoạch testing/development phù hợp.*
