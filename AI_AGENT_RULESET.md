# AI Agent Ruleset - Ông Ba Dạy Hóa Project

## 🎯 Mục Tiêu
Bộ quy tắc này giúp AI Agent:
1. **<PERSON><PERSON><PERSON> giá cấp độ kỹ thuật** của developer
2. **Hiểu sâu tech stack** (Next.js 15, Strapi 5, MySQL)
3. **Nắm bắt bối cảnh dự án** thông qua phân tích codebase
4. **Điều chỉnh hỗ trợ** phù hợp với từng cấp độ

---

## 📊 Đ<PERSON>h Giá Cấp Độ Kỹ Thuật

### 🟢 Beginner (Người mới bắt đầu)
**Đặc điểm nhận biết:**
- Chưa quen với Next.js App Router hoặc Strapi
- Cần hướng dẫn chi tiết từng bước
- Thường hỏi về cú pháp cơ bản và cấu trúc file
- Chưa hiểu rõ về API integration và authentication

**Cách hỗ trợ:**
- G<PERSON><PERSON>i thích từng bước một cách chi tiết
- Cung cấp code examples đầy đủ với comments
- G<PERSON><PERSON><PERSON> thích lý do tại sao làm như vậy
- Ưu tiên sử dụng patterns đã có trong codebase
- Tránh giới thiệu quá nhiều concepts mới cùng lúc

### 🟡 Intermediate (Trung cấp)
**Đặc điểm nhận biết:**
- Hiểu cơ bản về React và Next.js
- Có kinh nghiệm với REST API
- Cần hỗ trợ về best practices và optimization
- Muốn hiểu sâu hơn về kiến trúc hệ thống

**Cách hỗ trợ:**
- Giải thích concepts và trade-offs
- Đưa ra nhiều options với ưu/nhược điểm
- Focus vào performance và maintainability
- Giới thiệu advanced patterns khi phù hợp

### 🔴 Advanced (Nâng cao)
**Đặc điểm nhận biết:**
- Thành thạo với modern React patterns
- Hiểu rõ về system architecture
- Quan tâm đến scalability và security
- Có thể đưa ra technical decisions

**Cách hỗ trợ:**
- Thảo luận về architectural decisions
- Đề xuất optimizations và refactoring
- Focus vào advanced topics (caching, SSR/SSG, etc.)
- Collaborative problem-solving approach

---

## 🛠 Tech Stack Deep Dive

### Next.js 15 với App Router
**Key Concepts cần nắm:**
- File-based routing với `app/` directory
- Server Components vs Client Components
- Layouts và nested routing
- API Routes và Server Actions
- Middleware cho authentication

**Patterns trong dự án:**
```javascript
// Route structure: app/(routes)/[page-name]/page.js
// Layout hierarchy: layout.js -> nested layouts
// API client: app/api/strapi.js với Axios
// Authentication: JWT trong cookies + middleware.js
```

**Context7 Integration Points:**
- Sử dụng Context7 để tìm hiểu Next.js 15 features mới
- Phân tích App Router best practices
- Hiểu về React 19 integration

### Strapi 5 Headless CMS
**Key Concepts cần nắm:**
- Content Types và Relations
- Controllers, Services, Routes structure
- Plugin system (users-permissions, email, etc.)
- Custom API endpoints

**Patterns trong dự án:**
```javascript
// Structure: src/api/[entity]/controllers|services|routes
// Custom auth: src/api/auth/ với JWT
// Email integration: Brevo provider
// File uploads: Local storage với image optimization
```

**Context7 Integration Points:**
- Tìm hiểu Strapi 5 migration và new features
- Phân tích plugin development patterns
- Hiểu về TypeScript integration trong Strapi

### MySQL Database
**Key Concepts cần nắm:**
- Relational data modeling
- Knex.js query builder
- Connection pooling và optimization
- Migration strategies

**Patterns trong dự án:**
```javascript
// Config: backend/config/database.ts
// Connection: MySQL với SSL support
// Query optimization: Populate strategies trong Strapi
```

---

## 🔍 Phân Tích Codebase - Checklist

### 1. Kiến Trúc Tổng Thể
- [ ] Xác định monorepo structure (frontend/ + backend/)
- [ ] Hiểu data flow: Next.js ↔ Strapi ↔ MySQL
- [ ] Phân tích authentication flow (JWT + cookies)
- [ ] Xem xét API integration patterns

### 2. Frontend Analysis
- [ ] Route structure trong `app/(routes)/`
- [ ] Component organization trong `components/`
- [ ] Context providers và state management
- [ ] Tailwind CSS configuration và design system
- [ ] Custom hooks và utilities

### 3. Backend Analysis
- [ ] Content types và data models
- [ ] Custom controllers và services
- [ ] Plugin configurations
- [ ] Email templates và providers
- [ ] File upload và media handling

### 4. Development Workflow
- [ ] Package.json scripts và dependencies
- [ ] Environment variables setup
- [ ] Build và deployment processes
- [ ] Testing strategies (nếu có)

---

## 🎯 Context-Aware Support Strategies

### Khi Phân Tích Yêu Cầu
1. **Xác định scope:** Frontend, Backend, hay Full-stack?
2. **Đánh giá complexity:** Simple CRUD, Complex business logic, hay Integration?
3. **Kiểm tra existing patterns:** Có pattern tương tự trong codebase không?
4. **Xem xét impact:** Thay đổi này ảnh hưởng đến phần nào khác?

### Khi Đưa Ra Giải Pháp
1. **Tuân theo conventions:** Sử dụng naming và structure patterns hiện có
2. **Leverage existing code:** Tái sử dụng components, services, utilities
3. **Consider scalability:** Giải pháp có scale được không?
4. **Security first:** Đặc biệt quan trọng với authentication và data handling

### Khi Giải Thích Code
1. **Context matters:** Giải thích tại sao code được viết như vậy trong context này
2. **Show alternatives:** Đưa ra các cách khác và lý do chọn cách hiện tại
3. **Performance implications:** Giải thích impact về performance
4. **Maintenance considerations:** Code này dễ maintain không?

---

## 🚀 Action Templates

### Template 1: Feature Development
```markdown
1. Phân tích yêu cầu và xác định affected components
2. Kiểm tra existing patterns và reusable code
3. Design API endpoints (nếu cần) theo Strapi conventions
4. Implement frontend components theo project structure
5. Test integration và error handling
6. Document changes và update relevant files
```

### Template 2: Bug Fixing
```markdown
1. Reproduce issue và identify root cause
2. Kiểm tra related code và potential side effects
3. Fix issue với minimal changes
4. Test thoroughly across affected areas
5. Consider adding preventive measures
```

### Template 3: Optimization
```markdown
1. Profile current performance
2. Identify bottlenecks
3. Propose optimization strategies
4. Implement changes incrementally
5. Measure improvements
6. Document optimizations
```

---

## 📚 Context7 Integration Guidelines

### Khi Cần Tìm Hiểu Sâu
1. **Next.js specifics:** Sử dụng Context7 để tìm hiểu App Router, Server Components
2. **Strapi advanced:** Plugin development, custom fields, performance optimization
3. **MySQL optimization:** Query optimization, indexing strategies
4. **Integration patterns:** Best practices cho headless CMS architecture

### Khi Gặp Vấn Đề Phức Tạp
1. **Search for similar issues:** Tìm solutions trong documentation
2. **Understand trade-offs:** Phân tích pros/cons của different approaches
3. **Consider project context:** Solution phù hợp với current architecture không?

---

## ⚡ Quick Reference

### Common Commands
```bash
# Frontend
cd frontend && npm run dev    # Development server
cd frontend && npm run build  # Production build

# Backend  
cd backend && npm run develop # Strapi development
cd backend && npm run build   # Strapi build
```

### Key Files
- `frontend/app/api/strapi.js` - API client
- `backend/config/database.ts` - DB configuration
- `frontend/middleware.js` - Authentication middleware
- `backend/config/plugins.ts` - Strapi plugins

### Environment Variables
- Frontend: `NEXT_PUBLIC_STRAPI_URL`, `NEXT_PUBLIC_STRAPI_API_URL`
- Backend: `DATABASE_*`, `JWT_SECRET`, `GOOGLE_CLIENT_*`

---

## 🧠 Context7 Deep Integration Strategies

### Khi Sử Dụng Context7 cho Next.js
```markdown
Tìm kiếm: "Next.js 15 App Router"
Focus areas:
- Server Components vs Client Components patterns
- Streaming và Suspense implementation
- Route handlers và middleware
- Performance optimization techniques
- SEO và metadata handling
```

### Khi Sử Dụng Context7 cho Strapi
```markdown
Tìm kiếm: "Strapi 5 TypeScript"
Focus areas:
- Content Type relationships và population
- Custom controllers và services patterns
- Plugin development và lifecycle hooks
- Database query optimization
- Authentication và authorization strategies
```

### Khi Sử Dụng Context7 cho MySQL
```markdown
Tìm kiếm: "MySQL performance optimization"
Focus areas:
- Indexing strategies cho Strapi schema
- Connection pooling configuration
- Query optimization với Knex.js
- Database migration best practices
- Backup và recovery strategies
```

---

## 🔧 Advanced Problem-Solving Framework

### Level 1: Immediate Context Analysis
1. **Codebase Scan:** Tìm existing implementations tương tự
2. **Pattern Recognition:** Xác định patterns đang được sử dụng
3. **Dependency Check:** Kiểm tra related components/services
4. **Impact Assessment:** Đánh giá scope của changes

### Level 2: Deep Technical Analysis
1. **Architecture Review:** Fit với overall system design không?
2. **Performance Implications:** Bottlenecks và optimization opportunities
3. **Security Considerations:** Authentication, authorization, data validation
4. **Scalability Planning:** Solution có scale được không?

### Level 3: Context7-Enhanced Research
1. **Best Practices Lookup:** Tìm industry standards
2. **Alternative Approaches:** So sánh different solutions
3. **Integration Patterns:** Cách integrate với existing stack
4. **Future-Proofing:** Compatibility với upcoming versions

---

## 📋 Specialized Support Scenarios

### Scenario 1: Authentication & Authorization
**Context Analysis:**
- Current: JWT trong cookies + Strapi users-permissions
- Google OAuth integration
- Custom auth endpoints trong `backend/src/api/auth/`

**Support Strategy:**
- Beginner: Giải thích JWT flow và cookie handling
- Intermediate: Custom middleware và role-based access
- Advanced: Security hardening và OAuth2 flows

### Scenario 2: Database Operations
**Context Analysis:**
- MySQL với Knex.js query builder
- Strapi ORM abstractions
- Custom queries trong services

**Support Strategy:**
- Beginner: Basic CRUD operations qua Strapi API
- Intermediate: Complex queries và relationships
- Advanced: Performance optimization và custom SQL

### Scenario 3: Frontend State Management
**Context Analysis:**
- React Context cho global state
- Custom hooks cho data fetching
- Axios client với error handling

**Support Strategy:**
- Beginner: Props drilling vs Context usage
- Intermediate: Custom hooks patterns
- Advanced: State optimization và caching strategies

---

## 🎨 Code Quality Guidelines

### Consistency Checkers
1. **Naming Conventions:**
   - Components: PascalCase (LoginForm.jsx)
   - Files: kebab-case cho pages, camelCase cho utilities
   - APIs: RESTful với Vietnamese URLs

2. **Structure Patterns:**
   - Frontend: Component → Hook → API call
   - Backend: Route → Controller → Service → Database

3. **Error Handling:**
   - Frontend: Try-catch với user-friendly messages
   - Backend: Proper HTTP status codes và error objects

### Performance Patterns
1. **Frontend Optimization:**
   - Image optimization với Next.js Image component
   - Code splitting với dynamic imports
   - Caching strategies với SWR hoặc React Query

2. **Backend Optimization:**
   - Database query optimization
   - Proper populate strategies
   - Caching với Redis (nếu cần)

---

## 🚨 Common Pitfalls & Solutions

### Next.js App Router Pitfalls
- **Pitfall:** Mixing Server và Client Components incorrectly
- **Solution:** Clear separation, proper 'use client' directives

### Strapi Integration Pitfalls
- **Pitfall:** Over-populating relations causing performance issues
- **Solution:** Selective population based on actual needs

### MySQL Performance Pitfalls
- **Pitfall:** N+1 queries trong complex relationships
- **Solution:** Proper JOIN strategies và query optimization

---

## 📊 Success Metrics

### Code Quality Metrics
- Consistency với existing patterns: 95%+
- Performance impact: Minimal degradation
- Security compliance: 100%
- Documentation coverage: Complete for new features

### Developer Experience Metrics
- Time to understand solution: <30 minutes
- Implementation success rate: >90%
- Follow-up questions: <3 per feature

---

## 🔄 Continuous Improvement

### Feedback Loop
1. **Monitor:** Theo dõi developer questions và pain points
2. **Analyze:** Xác định recurring issues
3. **Update:** Cập nhật ruleset với new insights
4. **Validate:** Test với real scenarios

### Knowledge Base Evolution
- Thêm new patterns khi discover
- Update Context7 search strategies
- Refine support approaches based on effectiveness
- Document edge cases và solutions

---

*Bộ quy tắc này là living document, được cập nhật liên tục dựa trên project evolution và developer feedback.*
