# MCP Setup Guide - Ông Ba Dạy Hóa Project

## 🎯 Mục Tiêu
Hướng dẫn setup MCP (Model Context Protocol) tools để AI Agent có thể sử dụng các tools mạnh mẽ cho development.

## 🔧 MCP Tools Hiện Có Trong Augment

### **1. Context7 - Library Documentation**
- `resolve-library-id_Context_7`: Tìm library ID
- `get-library-docs_Context_7`: Lấy documentation chi tiết

### **2. Firecrawl - Web Scraping & Research**
- `firecrawl_scrape`: Scrape single page
- `firecrawl_search`: Search web với AI
- `firecrawl_crawl`: Crawl multiple pages
- `firecrawl_extract`: Extract structured data

### **3. Brave Search - Web Search**
- `brave_web_search`: General web search
- `brave_local_search`: Local business search

### **4. Browser Automation - Playwright**
- `browser_navigate`: Navigate to URLs
- `browser_snapshot`: Take page snapshots
- `browser_click`: Click elements
- `browser_type`: Type text
- And many more browser automation tools

## 📋 Setup Instructions

### **Step 1: Kiểm Tra MCP Tools Hiện Có**
Trong Augment, bạn có thể kiểm tra MCP tools available bằng cách:
1. Mở Augment settings
2. Tìm mục "MCP Servers" hoặc "Tools"
3. Xem danh sách tools hiện có

### **Step 2: Cấu Hình MCP Rules**
File `.augment/rules/mcp-tools-usage.md` đã được tạo với:
- Guidelines cho từng loại tool
- Workflow patterns cho common tasks
- Best practices cho research và development

### **Step 3: Test MCP Tools**
Thử các commands sau để test:

```
# Test Context7
"Tìm hiểu về Next.js 15 App Router patterns"

# Test Web Search
"Research Dokploy deployment best practices"

# Test Codebase Analysis
"Phân tích authentication flow trong codebase hiện tại"

# Test Browser Tools
"Test trang login trên production"
```

## 🎯 MCP Tools Usage Examples

### **Example 1: Research New Technology**
```
User: "Tôi muốn implement real-time notifications"
AI Agent sẽ:
1. Use Context7 để research WebSocket patterns
2. Use web search để tìm Strapi real-time solutions
3. Use codebase-retrieval để check existing patterns
4. Suggest implementation approach
```

### **Example 2: Debug Production Issue**
```
User: "Trang payment bị lỗi trên production"
AI Agent sẽ:
1. Use browser tools để navigate và reproduce issue
2. Use codebase-retrieval để analyze payment code
3. Use web search để find similar issues
4. Suggest fixes và testing approach
```

### **Example 3: Performance Optimization**
```
User: "Tối ưu performance cho 1K users"
AI Agent sẽ:
1. Use Context7 để research Next.js performance patterns
2. Use codebase-retrieval để analyze current implementation
3. Use browser tools để test performance
4. Implement optimizations
```

## 🚀 Advanced MCP Configuration

### **Custom MCP Server (Optional)**
Nếu bạn muốn tạo custom MCP server cho project-specific tools:

```json
// mcp-config.json
{
  "servers": {
    "ongbadayhoa-tools": {
      "command": "node",
      "args": ["./mcp-server/index.js"],
      "env": {
        "DATABASE_URL": "mysql://...",
        "STRAPI_URL": "https://be.ongbadayhoa.com"
      }
    }
  }
}
```

### **Project-Specific Tools Ideas:**
- **Database Query Tool**: Direct MySQL queries
- **Strapi API Tool**: Custom Strapi operations
- **Deployment Tool**: Dokploy integration
- **Analytics Tool**: User behavior analysis

## 📊 MCP Tools Benefits

### **For Development:**
✅ **Faster Research**: Context7 cho technical documentation  
✅ **Better Testing**: Browser automation cho UI testing  
✅ **Code Analysis**: Deep codebase understanding  
✅ **Web Research**: Latest best practices và solutions  

### **For Debugging:**
✅ **Production Testing**: Live site debugging  
✅ **Code Investigation**: Git history analysis  
✅ **Issue Research**: Similar problems và solutions  
✅ **Performance Analysis**: Real-time performance testing  

### **For Learning:**
✅ **Technology Research**: Deep dive vào new technologies  
✅ **Best Practices**: Industry standards và patterns  
✅ **Integration Patterns**: How technologies work together  
✅ **Security Research**: Latest security practices  

## 🛡️ Security & Best Practices

### **When Using MCP Tools:**
- **Sensitive Data**: Không share credentials qua tools
- **Production Access**: Cẩn thận khi test trên production
- **Rate Limiting**: Respect API limits của external services
- **Data Privacy**: Tuân thủ privacy policies

### **Tool Selection Strategy:**
1. **Start with codebase-retrieval** để hiểu existing code
2. **Use Context7** cho technical research
3. **Use web search** cho general information
4. **Use browser tools** cho testing và debugging

## 🎪 Getting Started

### **Immediate Actions:**
1. ✅ **Rules đã được tạo**: `.augment/rules/mcp-tools-usage.md`
2. 🔄 **Test MCP tools**: Thử các examples trên
3. 📝 **Feedback**: Adjust rules based on experience
4. 🚀 **Scale up**: Use tools cho real development tasks

### **Next Steps:**
- Test MCP tools với real project tasks
- Refine usage patterns based on results
- Consider custom MCP server nếu cần
- Train team members về MCP tools usage

---

**MCP tools sẽ transform cách AI Agent hỗ trợ development process của bạn! 🎓⚗️**
