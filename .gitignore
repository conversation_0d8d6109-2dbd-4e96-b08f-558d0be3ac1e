# Node modules
node_modules/
**/node_modules/

# Frontend build outputs
frontend/.next/
frontend/out/
frontend/build/

# Backend build outputs (nếu có)
backend/.cache/
backend/.strapi-updater.json

# Dependencies
frontend/node_modules/
backend/node_modules/

# Logs
npm-debug.log*
yarn-debug.log*
.pnpm-debug.log*

# Env files


# OS files
.DS_Store

# Coverage reports
coverage/
frontend/coverage/
backend/coverage/

# Generated sitemap/XML
frontend/public/sitemap.xml

# IDE files
.vscode/
.idea/
*.iws
*.iml
*.ipr

# STS
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
**/dist/
/nbdist/
/.nb-gradle/
build/
!/src/main//build/
!/src/test/**/build/

# Maven
target/
!.mvn/wrapper/maven-wrapper.jar
!/src/main//target/
!/src/test//target/

# Database dump
ongbadayhoa_db_dump.sql
backend/.strapi-updater.json
backend/dist/*
backend/src/.DS_Store
.vercel/project.json
frontend/.env.phucalaziz
